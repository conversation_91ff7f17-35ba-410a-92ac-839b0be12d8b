[app]
font_family = Segoe UI
font_size = 11
icon = Donut.ico
timezone_offset_hours = 8
window_width = 500
window_height = 467

[logging]
log_dir = logs
log_file = app.log

[user]
bcc_default = <EMAIL>
bcc_enabled_default = true

[websites]
jira大厅 = https://beigene.atlassian.net/issues/?filter=18992
betelligent后台 = https://rdai.beigenecorp.net/admin/#/admin/organization/user
betelligent_prod = https://rdai.beigenecorp.net/#/chat
betelligent uat 后台 = https://rdai-t.beigenecorp.net/admin/#/admin/organization/user
betelligent uat = https://rdai-t.beigenecorp.net/#/chat
netskope = C:\Users\<USER>\OneDrive - BeiGene\Plan\03Betelligent账号\Netskope Fix Script

[sendmail]
shortcut_label = Netskope问题排查
shortcut_cmd = C:\Users\<USER>\OneDrive - BeiGene\Plan\03Betelligent账号\Netskope Fix Script

[background]
mode = color
color = #00aaff
image = 
layout = center
brightness = 1.00

[teams]
message_text = Hi, Betelligent access has been granted.
	Please try to visit https://rdai.beigenecorp.net/#/chat.
	I will send the user manual to your email later.

[maintenance]
welcome_text = hi ，我是Betelligent管理员，请问现在是无法访问么 ？
	麻烦您帮我看看，您的电脑的Netskope软件的状态，
	右键点击图标，查看是否是 3个 绿灯。
default_image = C:/Users/<USER>/OneDrive - BeiGene/PortableSoftwareDir/brandnew 0926/Netskope Fix Script/3333.png
tbd_text = 请下载这个脚本到桌面，双击脚本执行，
	然后再检查Netskope 是否是三个绿灯，及
	Betelligent 是否正常访问。 
default_directory = C:/Users/<USER>/OneDrive - BeiGene/PortableSoftwareDir/brandnew 0926/Netskope Fix Script

