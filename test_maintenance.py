#!/usr/bin/env python3
"""
Test script for the maintenance tab functionality
"""
import sys
import os
from PIL import Image, ImageDraw

def create_test_image():
    """Create a simple test image"""
    # Create a simple test image
    img = Image.new('RGB', (200, 150), color='lightblue')
    draw = ImageDraw.Draw(img)
    draw.text((50, 70), "Test Image", fill='black')
    
    # Save to current directory
    test_image_path = os.path.join(os.getcwd(), "test_image.png")
    img.save(test_image_path)
    print(f"Test image created at: {test_image_path}")
    return test_image_path

def create_test_directory():
    """Create a test directory"""
    test_dir = os.path.join(os.getcwd(), "test_maintenance_dir")
    os.makedirs(test_dir, exist_ok=True)
    
    # Create a test file in the directory
    test_file = os.path.join(test_dir, "readme.txt")
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write("这是一个测试维修目录\n")
        f.write("用于测试维修Tab页的目录打开功能\n")
    
    print(f"Test directory created at: {test_dir}")
    return test_dir

def update_config_with_test_data():
    """Update config file with test data"""
    import configparser
    
    config_path = os.path.join("config", "config.ini")
    cfg = configparser.ConfigParser()
    cfg.read(config_path, encoding="utf-8")
    
    # Create test image and directory
    test_image = create_test_image()
    test_dir = create_test_directory()
    
    # Update maintenance section
    if not cfg.has_section("maintenance"):
        cfg.add_section("maintenance")
    
    cfg.set("maintenance", "welcome_text", "欢迎使用维修服务！\n这是一个测试欢迎词。")
    cfg.set("maintenance", "default_image", test_image)
    cfg.set("maintenance", "tbd_text", "这是TBD测试内容\n可以配置任何需要的信息。")
    cfg.set("maintenance", "default_directory", test_dir)
    
    # Save config
    with open(config_path, "w", encoding="utf-8") as f:
        cfg.write(f)
    
    print("Configuration updated with test data")
    print(f"- Test image: {test_image}")
    print(f"- Test directory: {test_dir}")

if __name__ == "__main__":
    try:
        update_config_with_test_data()
        print("\nTest setup completed successfully!")
        print("You can now run 'python main.py' to test the maintenance tab.")
    except ImportError:
        print("PIL (Pillow) is not installed. Installing...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
        update_config_with_test_data()
        print("\nTest setup completed successfully!")
        print("You can now run 'python main.py' to test the maintenance tab.")
    except Exception as e:
        print(f"Error setting up test: {e}")
