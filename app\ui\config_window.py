"""
Configuration window (independent). Supports websites table and placeholders for background/shortcut.
"""
from typing import Dict
import os

from app.common.utils import open_in_edge

try:
    from PyQt5 import QtWidgets, QtGui, QtCore
except Exception as e:  # pragma: no cover
    raise RuntimeError("PyQt5 is required to run this application.") from e


class WebsitesTab(QtWidgets.QWidget):
    def __init__(self, cfg, parent=None):
        super().__init__(parent)
        self.cfg = cfg
        layout = QtWidgets.QVBoxLayout(self)

        self.table = QtWidgets.QTableWidget(0, 2)
        self.table.setHorizontalHeaderLabels(["Name", "URL"])
        self.table.horizontalHeader().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeMode.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(1, QtWidgets.QHeaderView.ResizeMode.Stretch)

        btn_row = QtWidgets.QHBoxLayout()
        add_btn = QtWidgets.QPushButton("Add")
        edit_btn = QtWidgets.QPushButton("Edit")
        del_btn = QtWidgets.QPushButton("Delete")
        open_btn = QtWidgets.QPushButton("Open")
        add_btn.clicked.connect(self._add)
        edit_btn.clicked.connect(self._edit)
        del_btn.clicked.connect(self._delete)
        open_btn.clicked.connect(self._open)
        for b in (add_btn, edit_btn, del_btn, open_btn):
            btn_row.addWidget(b)
        btn_row.addStretch(1)

        layout.addWidget(self.table)
        layout.addLayout(btn_row)

        self._load()

    def _load(self):
        self.table.setRowCount(0)
        if self.cfg.has_section("websites"):
            for name, url in self.cfg.items("websites"):
                r = self.table.rowCount()
                self.table.insertRow(r)
                self.table.setItem(r, 0, QtWidgets.QTableWidgetItem(name))
                self.table.setItem(r, 1, QtWidgets.QTableWidgetItem(url))

    def _add(self):
        name, ok = QtWidgets.QInputDialog.getText(self, "Name", "Website name:")
        if not ok or not name:
            return
        url, ok = QtWidgets.QInputDialog.getText(self, "URL", "Website URL:")
        if not ok or not url:
            return
        if not self.cfg.has_section("websites"):
            self.cfg.add_section("websites")
        self.cfg.set("websites", name, url)
        self._load()
        self._save_cfg()

    def _edit(self):
        r = self.table.currentRow()
        if r < 0:
            return
        name = self.table.item(r, 0).text()
        url = self.table.item(r, 1).text()
        new_name, ok = QtWidgets.QInputDialog.getText(self, "Name", "Website name:", text=name)
        if not ok or not new_name:
            return
        new_url, ok = QtWidgets.QInputDialog.getText(self, "URL", "Website URL:", text=url)
        if not ok or not new_url:
            return
        if name != new_name and self.cfg.has_option("websites", name):
            self.cfg.remove_option("websites", name)
        self.cfg.set("websites", new_name, new_url)
        self._load()
        self._save_cfg()

    def _delete(self):
        r = self.table.currentRow()
        if r < 0:
            return
        name = self.table.item(r, 0).text()
        if self.cfg.has_option("websites", name):
            self.cfg.remove_option("websites", name)
            self._load()
            self._save_cfg()

    def _open(self):
        r = self.table.currentRow()
        if r < 0:
            return
        url = self.table.item(r, 1).text()
        open_in_edge(url)

    def _save_cfg(self):
        # Save back to config/config.ini
        import os
        from app.common.utils import DEFAULT_CONFIG_PATH
        with open(DEFAULT_CONFIG_PATH, "w", encoding="utf-8") as f:
            self.cfg.write(f)


class BackgroundTab(QtWidgets.QWidget):
    def __init__(self, cfg, parent=None):
        super().__init__(parent)
        self.cfg = cfg
        layout = QtWidgets.QVBoxLayout(self)

        # Mode selection
        self.mode_combo = QtWidgets.QComboBox()
        self.mode_combo.addItems(["color", "image"])
        self.mode_combo.setCurrentText(cfg.get("background", "mode", fallback="color"))

        # Color picker
        self.color_btn = QtWidgets.QPushButton("Pick Color")
        self.color_preview = QtWidgets.QLabel()
        self.color_preview.setFixedHeight(24)
        self._set_color_preview(cfg.get("background", "color", fallback="#ffffff"))

        # Image picker
        self.image_path = QtWidgets.QLineEdit(cfg.get("background", "image", fallback=""))
        browse_btn = QtWidgets.QPushButton("Browse…")
        browse_btn.clicked.connect(self._browse)
        self.layout_combo = QtWidgets.QComboBox()
        self.layout_combo.addItems(["center", "tile", "stretch"])
        self.layout_combo.setCurrentText(cfg.get("background", "layout", fallback="center"))

        # Brightness slider (0.2 - 1.5)
        self.brightness = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
        self.brightness.setMinimum(20)
        self.brightness.setMaximum(150)
        self.brightness.setValue(int(float(cfg.get("background", "brightness", fallback="1.0")) * 100))
        self.brightness_lbl = QtWidgets.QLabel(f"Brightness: {self.brightness.value()/100:.2f}")
        self.brightness.valueChanged.connect(lambda v: self.brightness_lbl.setText(f"Brightness: {v/100:.2f}"))

        # Preview area
        self.preview = QtWidgets.QFrame()
        self.preview.setFrameShape(QtWidgets.QFrame.Shape.StyledPanel)
        self.preview.setMinimumHeight(120)

        # Save button
        save_btn = QtWidgets.QPushButton("Save")
        save_btn.clicked.connect(self._save)

        # Layout assemble
        form = QtWidgets.QFormLayout()
        form.addRow("Mode", self.mode_combo)
        c_row = QtWidgets.QHBoxLayout()
        c_row.addWidget(self.color_btn)
        c_row.addWidget(self.color_preview, 1)
        form.addRow("Color", c_row)
        i_row = QtWidgets.QHBoxLayout()
        i_row.addWidget(self.image_path, 1)
        i_row.addWidget(browse_btn)
        form.addRow("Image", i_row)
        form.addRow("Layout", self.layout_combo)
        form.addRow(self.brightness_lbl, self.brightness)

        layout.addLayout(form)
        layout.addWidget(QtWidgets.QLabel("Preview:"))
        layout.addWidget(self.preview)
        layout.addWidget(save_btn)

        self.color_btn.clicked.connect(self._pick_color)
        self.mode_combo.currentTextChanged.connect(lambda _: self._update_preview())
        self.image_path.textChanged.connect(lambda _: self._update_preview())
        self.layout_combo.currentTextChanged.connect(lambda _: self._update_preview())
        self.brightness.valueChanged.connect(lambda _: self._update_preview())
        self._update_preview()

    def _set_color_preview(self, color_hex: str):
        self.color_preview.setStyleSheet(f"background:{color_hex}; border:1px solid #ccc;")

    def _pick_color(self):
        col = QtWidgets.QColorDialog.getColor()
        if col.isValid():
            self._set_color_preview(col.name())

    def _browse(self):
        path, _ = QtWidgets.QFileDialog.getOpenFileName(self, "Choose image", "", "Images (*.png *.jpg *.jpeg *.bmp)")
        if path:
            self.image_path.setText(path)

    def _update_preview(self):
        mode = self.mode_combo.currentText()
        brightness = self.brightness.value()/100.0
        if mode == "color":
            color_hex = self.color_preview.palette().window().color().name() if self.color_preview.palette() else self.cfg.get("background","color",fallback="#ffffff")
            self.preview.setStyleSheet(f"background-color:{color_hex}; filter:opacity({min(1.0,brightness)});")
        else:
            img = self.image_path.text()
            if os.path.exists(img):
                if self.layout_combo.currentText() == "tile":
                    self.preview.setStyleSheet(f"border-image:url('{img}') 0 0 0 0 repeat repeat; opacity:{min(1.0,brightness)};")
                elif self.layout_combo.currentText() == "stretch":
                    self.preview.setStyleSheet(f"border-image:url('{img}') 0 0 0 0 stretch stretch; opacity:{min(1.0,brightness)};")
                else:
                    self.preview.setStyleSheet(f"background-image:url('{img}'); background-position:center; background-repeat:no-repeat;")
            else:
                self.preview.setStyleSheet("")

    def _save(self):
        # Persist selections
        if not self.cfg.has_section("background"):
            self.cfg.add_section("background")
        mode = self.mode_combo.currentText()
        self.cfg.set("background", "mode", mode)
        if mode == "color":
            # Extract from color_preview style; fallback
            ss = self.color_preview.styleSheet()
            color = "#ffffff"
            if "background:" in ss:
                color = ss.split("background:",1)[1].split(";",1)[0].strip()
            self.cfg.set("background", "color", color)
        else:
            self.cfg.set("background", "image", self.image_path.text())
            self.cfg.set("background", "layout", self.layout_combo.currentText())
        self.cfg.set("background", "brightness", f"{self.brightness.value()/100.0:.2f}")
        from app.common.utils import DEFAULT_CONFIG_PATH
        with open(DEFAULT_CONFIG_PATH, "w", encoding="utf-8") as f:
            self.cfg.write(f)
        QtWidgets.QMessageBox.information(self, "Saved", "Background settings saved.")


class ShortcutTab(QtWidgets.QWidget):
    def __init__(self, cfg, parent=None):
        super().__init__(parent)
        self.cfg = cfg
        layout = QtWidgets.QVBoxLayout(self)
        layout.addWidget(QtWidgets.QLabel("Configure the STEP 5 shortcut label and command."))
        form = QtWidgets.QFormLayout()
        self.label_edit = QtWidgets.QLineEdit(cfg.get("sendmail", "shortcut_label", fallback="Open SOP"))
        self.cmd_edit = QtWidgets.QLineEdit(cfg.get("sendmail", "shortcut_cmd", fallback=""))
        form.addRow("Label", self.label_edit)
        form.addRow("Command", self.cmd_edit)
        save_btn = QtWidgets.QPushButton("Save")
        save_btn.clicked.connect(self._save)
        layout.addLayout(form)
        layout.addWidget(save_btn)

    def _save(self):
        if not self.cfg.has_section("sendmail"):
            self.cfg.add_section("sendmail")
        self.cfg.set("sendmail", "shortcut_label", self.label_edit.text())
        self.cfg.set("sendmail", "shortcut_cmd", self.cmd_edit.text())
        from app.common.utils import DEFAULT_CONFIG_PATH
        with open(DEFAULT_CONFIG_PATH, "w", encoding="utf-8") as f:
            self.cfg.write(f)
        QtWidgets.QMessageBox.information(self, "Saved", "Shortcut updated.")


class TeamsTab(QtWidgets.QWidget):
    def __init__(self, cfg, parent=None):
        super().__init__(parent)
        self.cfg = cfg
        layout = QtWidgets.QVBoxLayout(self)
        layout.addWidget(QtWidgets.QLabel("Configure STEP 4 message label (Teams)."))
        self.msg_edit = QtWidgets.QTextEdit()
        self.msg_edit.setPlainText(cfg.get("teams", "message_text", fallback="Please join the Internal Teams group for updates."))
        save_btn = QtWidgets.QPushButton("Save")
        save_btn.clicked.connect(self._save)
        layout.addWidget(self.msg_edit, 1)
        layout.addWidget(save_btn)

    def _save(self):
        if not self.cfg.has_section("teams"):
            self.cfg.add_section("teams")
        self.cfg.set("teams", "message_text", self.msg_edit.toPlainText())
        from app.common.utils import DEFAULT_CONFIG_PATH
        with open(DEFAULT_CONFIG_PATH, "w", encoding="utf-8") as f:
            self.cfg.write(f)
        # Notify listeners (e.g., MainWindow) if available
        win = self.window()
        if hasattr(win, 'settings_saved'):
            win.settings_saved.emit({"section": "teams"})
        QtWidgets.QMessageBox.information(self, "Saved", "Teams message updated.")


class ConfigWindow(QtWidgets.QMainWindow):
    settings_saved = QtCore.pyqtSignal(dict)
    def __init__(self, cfg, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Settings")
        self.cfg = cfg
        tabs = QtWidgets.QTabWidget()
        tabs.addTab(WebsitesTab(cfg), "Websites")
        tabs.addTab(BackgroundTab(cfg), "Background")
        tabs.addTab(ShortcutTab(cfg), "Shortcut")
        tabs.addTab(TeamsTab(cfg), "Teams")
        self.setCentralWidget(tabs)

