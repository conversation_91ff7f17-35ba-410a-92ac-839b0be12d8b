"""
Standalone statistics script parsing logs/app.log.
- Shows unique users for last week/month or custom date range.
- Groups both email domains as a single user (by local-part).
- If matplotlib is available, shows a simple bar chart; else prints textual report.
"""
import argparse
import json
import os
from datetime import datetime, timedelta, timezone
from collections import Counter, defaultdict


def parse_args():
    p = argparse.ArgumentParser()
    p.add_argument("--log", default=os.path.join("logs", "app.log"))
    p.add_argument("--period", choices=["week", "month", "custom"], default="week")
    p.add_argument("--start")
    p.add_argument("--end")
    return p.parse_args()


def parse_ts(z):
    # Expecting ISO with timezone info; ensure UTC if not provided
    s = z
    if s.endswith("Z"):
        s = s.replace("Z", "+00:00")
    dt = datetime.fromisoformat(s)
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    return dt


def local_part(email: str) -> str:
    email = email.strip()
    if "@" in email:
        return email.split("@", 1)[0].lower()
    return email.lower()


def load_records(log_path: str):
    if not os.path.exists(log_path):
        return []
    out = []
    with open(log_path, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            try:
                rec = json.loads(line)
                out.append(rec)
            except Exception:
                continue
    return out


def filter_user_group(records, start: datetime, end: datetime):
    return [r for r in records if r.get("group") == "User" and r.get("action") == "copy" and start <= parse_ts(r.get("ts", "1970-01-01T00:00:00+00:00")) <= end]


def summarize(records):
    # Count unique local-parts by day
    per_day = defaultdict(set)
    for r in records:
        ts = parse_ts(r["ts"]).date()
        lp = local_part(r.get("detail", ""))
        per_day[ts].add(lp)
    daily_counts = {d: len(u) for d, u in per_day.items()}
    total_users = len(set().union(*per_day.values())) if per_day else 0
    return daily_counts, total_users


def maybe_plot(daily_counts):
    try:
        import matplotlib.pyplot as plt
    except Exception:
        # Fallback textual bar chart
        if not daily_counts:
            print("No data to plot.")
            return
        print("\nASCII Chart (unique users per day):")
        width = max(daily_counts.values())
        for d in sorted(daily_counts):
            cnt = daily_counts[d]
            bar = "#" * max(1, int(cnt))
            print(f"{d}: {bar} ({cnt})")
        return
    if not daily_counts:
        print("No data to plot.")
        return
    xs = sorted(daily_counts)
    ys = [daily_counts[d] for d in xs]
    plt.figure(figsize=(8,3))
    plt.bar([d.strftime("%m-%d") for d in xs], ys)
    plt.title("Unique Users per Day (User group)")
    plt.xlabel("Date")
    plt.ylabel("Unique Users")
    plt.tight_layout()
    plt.show()


def main():
    args = parse_args()
    records = load_records(args.log)

    now = datetime.now(timezone.utc)
    if args.period == "week":
        start = now - timedelta(days=7)
        end = now
    elif args.period == "month":
        start = now - timedelta(days=30)
        end = now
    else:
        if not args.start or not args.end:
            raise SystemExit("For custom period, provide --start YYYY-MM-DD and --end YYYY-MM-DD")
        start = datetime.fromisoformat(args.start)
        end = datetime.fromisoformat(args.end)
        if start.tzinfo is None:
            start = start.replace(tzinfo=timezone.utc)
        if end.tzinfo is None:
            end = end.replace(tzinfo=timezone.utc)

    user_records = filter_user_group(records, start, end)
    daily_counts, total = summarize(user_records)

    print(f"Log: {args.log}")
    print(f"Range: {start} to {end}")
    print(f"Total unique users (grouped domains): {total}")
    if daily_counts:
        print("Per-day unique counts:")
        for d in sorted(daily_counts):
            print(f"  {d}: {daily_counts[d]}")
    maybe_plot(daily_counts)


if __name__ == "__main__":
    main()

