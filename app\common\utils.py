"""
Pure utility functions for config, logging, validation, OS helpers.
Functional style: functions are stateless; data flows via arguments/returns.
"""
import os
import sys
import json
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Tuple, Optional

try:
    import configparser
except Exception:  # pragma: no cover
    configparser = None

APP_ROOT = os.path.dirname(os.path.abspath(os.path.join(__file__, "..", "..")))
DEFAULT_CONFIG_PATH = os.path.join(APP_ROOT, "config", "config.ini")
DEFAULT_LOG_DIR = os.path.join(APP_ROOT, "logs")


def ensure_dirs(paths: List[str]) -> None:
    for p in paths:
        os.makedirs(p, exist_ok=True)


def load_config(path: str = DEFAULT_CONFIG_PATH) -> "configparser.ConfigParser":
    if configparser is None:
        raise RuntimeError("configparser is unavailable in this Python environment.")
    cfg = configparser.ConfigParser()
    if os.path.exists(path):
        cfg.read(path, encoding="utf-8")
    else:
        # Create directories and empty config if missing to avoid crashes
        ensure_dirs([os.path.dirname(path)])
        with open(path, "w", encoding="utf-8") as f:
            f.write("")
        cfg.read(path, encoding="utf-8")
    return cfg


def tz_from_offset_hours(offset: int) -> timezone:
    return timezone(timedelta(hours=offset))


def now_local_str(offset_hours: int) -> str:
    tz = tz_from_offset_hours(offset_hours)
    return datetime.now(tz).strftime("%Y-%m-%d %H:%M:%S")


def normalize_email_dual_domains(email: str) -> List[str]:
    """Given an email, if it ends with beigene.com or beonemed.com, return both variants.
    Otherwise return the email itself if it looks like an email.
    """
    email = email.strip()
    if "@" not in email:
        return []
    local, _, domain = email.partition("@")
    domain = domain.lower()
    if domain in ("beigene.com", "beonemed.com"):
        return [f"{local}@beigene.com", f"{local}@beonemed.com"]
    return [email]


def setup_logging(cfg) -> str:
    log_dir = cfg.get("logging", "log_dir", fallback=DEFAULT_LOG_DIR)
    if not os.path.isabs(log_dir):
        log_dir = os.path.join(APP_ROOT, log_dir)
    ensure_dirs([log_dir])
    log_file = cfg.get("logging", "log_file", fallback="app.log")
    log_path = os.path.join(log_dir, log_file)
    return log_path


def append_log(log_path: str, action: str, group: str, detail: str, extra: Optional[Dict]=None) -> None:
    # Use timezone-aware timestamp in UTC for robust parsing
    ts = datetime.now(timezone.utc).isoformat(timespec="seconds")
    record = {
        "ts": ts,
        "action": action,
        "group": group,
        "detail": detail,
        **(extra or {}),
    }
    with open(log_path, "a", encoding="utf-8") as f:
        f.write(json.dumps(record, ensure_ascii=False) + "\n")


def open_in_edge(url: str) -> bool:
    """Open a URL in Microsoft Edge. Returns True on best-effort success."""
    import webbrowser
    # Prefer Edge if registered; otherwise default browser
    edge_paths = [
        "MicrosoftEdge",  # URI scheme handler
    ]
    for name in edge_paths:
        try:
            return webbrowser.get(name).open(url)
        except Exception:
            continue
    return webbrowser.open(url)


def resource_path(relative: str) -> str:
    base_path = getattr(sys, '_MEIPASS', APP_ROOT)
    return os.path.join(base_path, relative)


def local_part(email: str) -> str:
    """Return the part before '@'. If '@' missing, return the whole string.
    Always trimmed; original casing preserved.
    """
    s = email.strip()
    return s.split('@', 1)[0] if s else ""


