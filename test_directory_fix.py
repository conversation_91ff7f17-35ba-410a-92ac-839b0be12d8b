#!/usr/bin/env python3
"""
Test script to verify the directory opening functionality fix
"""
import os
import configparser

def test_directory_opening():
    """Test the directory opening functionality"""
    print("🔧 测试目录打开功能修复")
    print("=" * 50)
    
    # Read config
    config_path = os.path.join("config", "config.ini")
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return
    
    cfg = configparser.ConfigParser()
    cfg.read(config_path, encoding="utf-8")
    
    if not cfg.has_section("maintenance"):
        print("❌ 维修配置部分不存在")
        return
    
    default_dir = cfg.get("maintenance", "default_directory", fallback="")
    print(f"📁 配置的目录路径: {default_dir}")
    
    if not default_dir:
        print("❌ 未配置默认目录")
        return
    
    # Normalize path
    normalized_dir = os.path.normpath(default_dir)
    print(f"📁 标准化后路径: {normalized_dir}")
    
    # Check if directory exists
    if os.path.exists(normalized_dir):
        print("✅ 目录存在")
        
        # Test opening directory
        try:
            print("🚀 尝试打开目录...")
            os.startfile(normalized_dir)
            print("✅ 目录打开成功！")
        except Exception as e:
            print(f"❌ 目录打开失败: {str(e)}")
    else:
        print("❌ 目录不存在")
        
        # Try to create the directory for testing
        try:
            os.makedirs(normalized_dir, exist_ok=True)
            print(f"✅ 已创建测试目录: {normalized_dir}")
            
            # Create a test file
            test_file = os.path.join(normalized_dir, "test.txt")
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("这是一个测试文件\n")
                f.write("用于验证目录打开功能\n")
            print(f"✅ 已创建测试文件: {test_file}")
            
            # Try opening again
            print("🚀 再次尝试打开目录...")
            os.startfile(normalized_dir)
            print("✅ 目录打开成功！")
            
        except Exception as e:
            print(f"❌ 创建目录失败: {str(e)}")

def show_component_order():
    """Show the new component order"""
    print("\n🔄 组件顺序调整")
    print("=" * 50)
    print("新的组件顺序:")
    print("1. 欢迎词 (Group 1)")
    print("2. TBD (Group 2) ← 从第3位移到第2位")
    print("3. 图片 (Group 3) ← 从第2位移到第3位")
    print("4. 工具目录 (Group 4)")
    print()
    print("✅ TBD组件和图片组件位置已调换")

if __name__ == "__main__":
    test_directory_opening()
    show_component_order()
    
    print("\n" + "=" * 50)
    print("🎉 修复完成！")
    print("1. ✅ 目录打开功能已修复 (使用os.startfile)")
    print("2. ✅ TBD和图片组件位置已调换")
    print("3. 🚀 可以运行 'python main.py' 测试功能")
