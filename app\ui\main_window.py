"""
Main Window implementation.
Functional-style where feasible; UI methods call pure functions in common.utils.
"""
from typing import List
import os

from app.common.utils import (
    load_config,
    setup_logging,
    append_log,
    now_local_str,
    normalize_email_dual_domains,
    resource_path,
    open_in_edge,
)

try:
    from PyQt5 import QtWidgets, QtGui, QtCore
except Exception as e:  # pragma: no cover
    raise RuntimeError("PyQt5 is required to run this application.") from e


def build_font(cfg):
    family = cfg.get("app", "font_family", fallback="Segoe UI")
    size = cfg.getint("app", "font_size", fallback=11)
    font = QtGui.QFont(family, pointSize=size)
    font.setBold(False)
    return font


class MainWindow(QtWidgets.QMainWindow):
    def __init__(self, cfg, log_path):
        super().__init__()
        self.cfg = cfg
        self.log_path = log_path
        self.setWindowTitle("IT Help Desk Access Tool")
        icon_name = cfg.get("app", "icon", fallback="Donut.ico")
        candidate_icons = [
            resource_path(os.path.join("app", "data", icon_name)),
            resource_path(icon_name),
        ]
        for ip in candidate_icons:
            if os.path.exists(ip):
                self.setWindowIcon(QtGui.QIcon(ip))
                break
        # self.resize(900, 600)
        self.resize(700, 467)

        self.base_font = build_font(cfg)
        self.setFont(self.base_font)

        # Central widget with tabs
        self.tabs = QtWidgets.QTabWidget()
        self.setCentralWidget(self.tabs)

        # Build tabs
        self.email_tab = self._build_email_tab()
        self.tabs.addTab(self.email_tab, "Betelligent")

        # Add maintenance tab
        self.maintenance_tab = self._build_maintenance_tab()
        self.tabs.addTab(self.maintenance_tab, "维修")

        # Status bar clock
        self.clock = QtWidgets.QLabel()
        self.statusBar().addPermanentWidget(self.clock)
        # Observe config window saves to refresh dynamic labels
        try:
            from app.ui.config_window import ConfigWindow
            ConfigWindow.settings_saved.connect(self._on_settings_saved)
        except Exception:
            pass

        self._start_clock()

        # Menus
        self._build_menus()

    def _start_clock(self):
        offset = self.cfg.getint("app", "timezone_offset_hours", fallback=8)
        def tick():
            self.clock.setText(f"UTC+{offset} "+ now_local_str(offset))
        self._clock_timer = QtCore.QTimer(self)
        self._clock_timer.timeout.connect(tick)
        self._clock_timer.start(1000)
        tick()

    def _build_menus(self):
        menubar = self.menuBar()

        goto_menu = menubar.addMenu("Goto")
        for key, url in self.cfg.items("websites") if self.cfg.has_section("websites") else []:
            action = QtWidgets.QAction(key, self)
            action.triggered.connect(lambda _=False, u=url: open_in_edge(u))
            goto_menu.addAction(action)

        set_menu = menubar.addMenu("Set")
        cfg_action = QtWidgets.QAction("Open Configuration", self)
        cfg_action.triggered.connect(self._open_config_window)
        set_menu.addAction(cfg_action)

        stats_action = QtWidgets.QAction("Statistics", self)
        stats_action.triggered.connect(self._open_stats)
        set_menu.addAction(stats_action)

    def _open_config_window(self):
        from app.ui.config_window import ConfigWindow
        # Keep config window independent of main; do not parent it
        self._cfg_win = ConfigWindow(self.cfg, None)
        self._cfg_win.setAttribute(QtCore.Qt.WidgetAttribute.WA_QuitOnClose, False)
        # Connect signal so dynamic labels update when settings are saved
        try:
            self._cfg_win.settings_saved.connect(self._on_settings_saved)
        except Exception:
            pass
        self._cfg_win.show()
        self._cfg_win.raise_()
        self._cfg_win.activateWindow()

    def _open_stats(self):
        # Launch stats script in a new process
        import subprocess, sys
        script = resource_path(os.path.join("tools", "stats.py"))
        subprocess.Popen([sys.executable, script, "--log", self.log_path])

    def _teams_message_text(self) -> str:
        msg = self.cfg.get("teams", "message_text", fallback="")
        # return f"Message: {msg}"
        return f"{msg}"

    @QtCore.pyqtSlot(dict)
    def _on_settings_saved(self, payload: dict):
        # Refresh STEP 4 label when Teams message updated
        try:
            section = (payload or {}).get("section", "")
        except Exception:
            section = ""
        if section == "teams" and hasattr(self, "teams_lbl"):
            self.teams_lbl.setText(self._teams_message_text())
        elif section == "maintenance":
            # Refresh maintenance tab content
            if hasattr(self, "welcome_text"):
                welcome_text_content = self.cfg.get("maintenance", "welcome_text", fallback="欢迎使用维修服务！")
                self.welcome_text.setPlainText(welcome_text_content)
            if hasattr(self, "tbd_text"):
                tbd_text_content = self.cfg.get("maintenance", "tbd_text", fallback="待定内容")
                self.tbd_text.setPlainText(tbd_text_content)
            if hasattr(self, "image_label"):
                default_image_path = self.cfg.get("maintenance", "default_image", fallback="")
                if default_image_path and os.path.exists(default_image_path):
                    self._load_image(default_image_path)
                else:
                    self.image_label.setText("无图片")
                    self.image_label.setPixmap(QtGui.QPixmap())

    def _build_email_tab(self):
        w = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(w)

        # Internal Organization label
        org_lbl = QtWidgets.QLabel("")
        layout.addWidget(org_lbl)

        # STEP 1-4 group boxes could be further structured; we focus on STEP 1 (User)
        # STEP 1: User group
        user_group = QtWidgets.QGroupBox("STEP 1: User")

        user_group.setStyleSheet("""
        QGroupBox {
            background-color: #f0f8ff;  /* 淡蓝色 */
            border: 2px solid #0078d7;
            border-radius: 5px;
            margin-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px 0 3px;
            color: #0078d7;
            font-weight: bold;
        }
        """)




        user_layout = QtWidgets.QVBoxLayout(user_group)

        self.email_input = QtWidgets.QLineEdit()
        ib_font = self.base_font
        ib_font.setBold(True)
        self.email_input.setFont(ib_font)
        self.email_input.setMinimumHeight(36)
        self.email_input.setPlaceholderText("Enter user email (e.g., <EMAIL>)")
        self.email_input.textChanged.connect(self._on_email_changed)

        clear_btn = QtWidgets.QToolButton()
        clear_btn.setText("Clear")
        clear_btn.setMinimumHeight(36)
        clear_btn.clicked.connect(lambda: self.email_input.setText(""))

        row = QtWidgets.QHBoxLayout()
        row.addWidget(self.email_input, 1)
        row.addWidget(clear_btn)
        user_layout.addLayout(row)

        self.user_list = QtWidgets.QListWidget()
        user_layout.addWidget(self.user_list)

        layout.addWidget(user_group)

        # STEP 2: Add Internal Origanization
        step2_group = QtWidgets.QGroupBox("STEP 2: Add Internal Origanization")

        step2_group.setStyleSheet("""
        QGroupBox {
            background-color: #fffbe6;  /* 淡黄色 */
            border: 2px solid #faad14;
            border-radius: 5px;
            margin-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px 0 3px;
            color: #faad14;
            font-weight: bold;
        }
        """)
        

        step2_layout = QtWidgets.QVBoxLayout(step2_group)
        step2_layout.addWidget(QtWidgets.QLabel("Add Internal Origanization"))
        layout.addWidget(step2_group)

        # STEP 3: Identification
        step3_group = QtWidgets.QGroupBox("STEP 3: Identification")

        step3_group.setStyleSheet("""
        QGroupBox {
            background-color: #f6ffed;  /* 淡绿色 */
            border: 2px solid #52c41a;
            border-radius: 5px;
            margin-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px 0 3px;
            color: #389e0d;
            font-weight: bold;
        }
        """)




        step3_layout = QtWidgets.QHBoxLayout(step3_group)
        self.ident_label = QtWidgets.QLineEdit()
        self.ident_label.setReadOnly(True)
        copy_ident_btn = QtWidgets.QPushButton("Copy")
        copy_ident_btn.setMinimumHeight(32)
        copy_ident_btn.clicked.connect(lambda: self._copy_text("STEP3-Identification", self.ident_label.text(), include_in_stats=False))
        step3_layout.addWidget(self.ident_label, 1)
        step3_layout.addWidget(copy_ident_btn)
        layout.addWidget(step3_group)

        # STEP 4: Teams group with configurable message copy
        teams_group = QtWidgets.QGroupBox("STEP 4: Teams")
        teams_group.setStyleSheet("""
        QGroupBox {
            background-color: #f9f0ff;  /* 淡紫色 */
            border: 2px solid #722ed1;
            border-radius: 5px;
            margin-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px 0 3px;
            color: #722ed1;
            font-weight: bold;
        }
        """)




        teams_layout = QtWidgets.QHBoxLayout(teams_group)
        self.teams_lbl = QtWidgets.QLabel(self._teams_message_text())
        copy_msg_btn = QtWidgets.QPushButton("Copy")
        copy_msg_btn.setMinimumHeight(32)
        copy_msg_btn.clicked.connect(lambda: self._copy_text(
            "STEP4-Teams", self._teams_message_text(), include_in_stats=False))
        teams_layout.addWidget(self.teams_lbl, 1)
        teams_layout.addWidget(copy_msg_btn)
        layout.addWidget(teams_group)

        # STEP 5: Sendmail group
        send_group = QtWidgets.QGroupBox("STEP 5: Sendmail")

        send_group.setStyleSheet("""
        QGroupBox {
            background-color: #fff0f6;  /* 淡粉色 */
            border: 2px solid #eb2f96;
            border-radius: 5px;
            margin-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px 0 3px;
            color: #eb2f96;
            font-weight: bold;
        }
        """)





        send_layout = QtWidgets.QVBoxLayout(send_group)

        self.to_edit = QtWidgets.QLineEdit()
        self.to_edit.setPlaceholderText("To: <EMAIL>; <EMAIL>")
        self.to_edit.setMinimumHeight(32)
        bcc_edit = QtWidgets.QLineEdit()
        bcc_edit.setPlaceholderText("BCC")
        bcc_edit.setMinimumHeight(32)
        bcc_enabled = QtWidgets.QCheckBox("Enable BCC")
        bcc_default = self.cfg.get("user", "bcc_default", fallback="")
        bcc_edit.setText(bcc_default)
        bcc_enabled.setChecked(self.cfg.getboolean("user", "bcc_enabled_default", fallback=True))

        send_btn = QtWidgets.QPushButton("Send HTML via Outlook")

        # 设置按钮颜色和样式
        send_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;   /* 绿色背景 */
                color: white;                /* 白色字体 */
                border-radius: 8px;          /* 圆角 */
                font-size: 16px;             /* 字体大小 */
                padding: 8px 24px;           /* 内边距 */
            }
            QPushButton:hover {
                background-color: #45a049;   /* 悬停时变色 */
            }
        """)
        send_btn.setFixedWidth(400)
        send_btn.setMinimumHeight(36)
        send_btn.clicked.connect(lambda: self._send_template_mail(self.to_edit.text(), bcc_edit.text() if bcc_enabled.isChecked() else ""))

        # Shortcut
        shortcut_row = QtWidgets.QHBoxLayout()
        shortcut_label = self.cfg.get("sendmail", "shortcut_label", fallback="Open SOP")
        shortcut_cmd = self.cfg.get("sendmail", "shortcut_cmd", fallback="")
        shortcut_btn = QtWidgets.QPushButton(shortcut_label)
        shortcut_btn.setMinimumHeight(32)
        shortcut_btn.clicked.connect(lambda: open_in_edge(shortcut_cmd) if shortcut_cmd else None)
        shortcut_row.addWidget(shortcut_btn)
        send_layout.addLayout(shortcut_row)

        send_layout.addWidget(self.to_edit)
        row2 = QtWidgets.QHBoxLayout()
        row2.addWidget(bcc_edit)
        row2.addWidget(bcc_enabled)
        send_layout.addLayout(row2)
        send_layout.addWidget(send_btn,alignment=QtCore.Qt.AlignRight)

        # Last 15 sent
        self.sent_list = QtWidgets.QListWidget()
        send_layout.addWidget(self.sent_list)

        layout.addWidget(send_group)

        layout.addStretch(1)
        return w

    def _on_email_changed(self, text: str):
        emails = normalize_email_dual_domains(text)
        # Update STEP 1 list
        self.user_list.clear()
        for e in emails:
            item_w = QtWidgets.QWidget()
            h = QtWidgets.QHBoxLayout(item_w)
            lbl = QtWidgets.QLabel(e)
            btn = QtWidgets.QPushButton("Copy")
            btn.setMinimumHeight(32)
            btn.clicked.connect(lambda _=False, email=e: self._copy_text("User", email, include_in_stats=True))
            h.addWidget(lbl, 1)
            h.addWidget(btn)
            h.setContentsMargins(0,0,0,0)
            item = QtWidgets.QListWidgetItem()
            self.user_list.addItem(item)
            self.user_list.setItemWidget(item, item_w)
            item.setSizeHint(item_w.sizeHint())
        # Update STEP 3 identification
        from app.common.utils import local_part
        ident = local_part(text)
        self.ident_label.setText(ident)
        # Autofill STEP 5 To field with the generated emails
        if hasattr(self, 'to_edit'):
            self.to_edit.setText("; ".join(emails))

    def _copy_text(self, group: str, text: str, include_in_stats: bool):
        cb = QtWidgets.QApplication.clipboard()
        cb.setText(text)
        append_log(self.log_path, action="copy", group=group, detail=text, extra={"include_in_stats": include_in_stats})

    def _send_template_mail(self, to_field: str, bcc_field: str):
        # Outlook COM via pywin32
        try:
            import win32com.client  # type: ignore
        except Exception:
            QtWidgets.QMessageBox.warning(self, "Outlook Missing", "pywin32 / Outlook COM is unavailable on this system.")
            return
        try:
            # Locate template and its resource folder
            from app.common.utils import APP_ROOT
            template_name = "Notification for Betelligent account is activated.htm"
            template_path = os.path.join(APP_ROOT, template_name)
            res_dir = os.path.splitext(template_path)[0] + ".files"

            # Read HTML with robust encoding handling
            def _read_html(path: str) -> str:
                for enc in ("utf-8", "utf-16", "utf-16-le", "utf-16-be", "latin-1"):
                    try:
                        with open(path, "r", encoding=enc) as f:
                            return f.read()
                    except Exception:
                        continue
                # Last resort binary decode
                with open(path, "rb") as f:
                    data = f.read()
                try:
                    return data.decode("utf-16")
                except Exception:
                    return data.decode("utf-8", errors="ignore")

            html = _read_html(template_path)

            # Remove Word-generated filelist.xml link tags entirely
            import re
            html = re.sub(r'<link[^>]+filelist\.xml[^>]*>', '', html, flags=re.IGNORECASE)

            # Attach resources and rewrite to cid: links
            outlook = win32com.client.Dispatch("Outlook.Application")
            mail = outlook.CreateItem(0)
            mail.To = to_field
            if bcc_field:
                mail.BCC = bcc_field
            mail.Subject = "Notification for Betelligent account is activated"

            # Map of original relative path -> cid
            cid_map = {}
            def _attach_and_cid(rel_path: str) -> str:
                # Skip attaching filelist.xml explicitly
                if os.path.basename(rel_path).lower() == "filelist.xml":
                    return rel_path
                # Build absolute path relative to template directory
                abs_path = os.path.join(os.path.dirname(template_path), rel_path)
                if not os.path.isabs(abs_path):
                    abs_path = os.path.abspath(abs_path)
                if not os.path.exists(abs_path):
                    # Try resolve within .files folder if not already
                    candidate = os.path.join(res_dir, os.path.basename(rel_path))
                    if os.path.exists(candidate):
                        abs_path = candidate
                    else:
                        return rel_path
                import mimetypes
                att = mail.Attachments.Add(abs_path)
                # Set MAPI properties to inline the resource so it does not appear as an attachment
                cid = os.path.basename(abs_path)
                try:
                    pa = att.PropertyAccessor
                    # PR_ATTACH_CONTENT_ID (string)
                    pa.SetProperty("http://schemas.microsoft.com/mapi/proptag/0x3712001F", cid)
                    # PR_ATTACHMENT_HIDDEN (boolean)
                    pa.SetProperty("http://schemas.microsoft.com/mapi/proptag/0x7FFE000B", True)
                    # PR_ATTACH_FLAGS (int): 0x00000004 (ATT_MHTML_REF) marks as referenced by HTML
                    pa.SetProperty("http://schemas.microsoft.com/mapi/proptag/0x37140003", 0x00000004)
                    # PR_ATTACH_CONTENT_DISPOSITION (string): inline
                    pa.SetProperty("http://schemas.microsoft.com/mapi/proptag/0x3716001F", "inline")
                    # PR_ATTACH_MIME_TAG (string): best-effort mime type
                    mt, _ = mimetypes.guess_type(abs_path)
                    if mt:
                        pa.SetProperty("http://schemas.microsoft.com/mapi/proptag/0x370E001F", mt)
                except Exception:
                    pass
                return cid

            def replace_match(m):
                attr = m.group(1)
                url = m.group(2)
                # Only process local resources (no http/https)
                if url.lower().startswith("http"):
                    return m.group(0)
                # Do not inline or alter filelist.xml
                if url.lower().endswith("filelist.xml"):
                    return m.group(0)
                # Normalize and handle .files resources
                if url not in cid_map:
                    cid_map[url] = _attach_and_cid(url)
                return f'{attr}="cid:{cid_map[url]}"'

            # Replace src/href that point to local files
            html_updated = re.sub(r'(src|href)="([^"]+)"', replace_match, html, flags=re.IGNORECASE)
            mail.HTMLBody = html_updated
            mail.Send()  # send immediately

            append_log(self.log_path, action="sendmail", group="STEP5-Sendmail", detail=to_field, extra={"bcc": bool(bcc_field)})
            # Keep list of last 15
            self.sent_list.insertItem(0, f"{now_local_str(self.cfg.getint('app','timezone_offset_hours',fallback=8))} -> {to_field}")
            while self.sent_list.count() > 15:
                self.sent_list.takeItem(self.sent_list.count()-1)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Outlook Error", str(e))

    def _build_maintenance_tab(self):
        """Build the maintenance tab with configurable groups"""
        w = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(w)

        # Group 1: Welcome message
        welcome_group = QtWidgets.QGroupBox("欢迎词")
        welcome_group.setStyleSheet("""
        QGroupBox {
            background-color: #f0f8ff;  /* 淡蓝色 */
            border: 2px solid #0078d7;
            border-radius: 5px;
            margin-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px 0 3px;
            color: #0078d7;
            font-weight: bold;
        }
        """)

        welcome_layout = QtWidgets.QHBoxLayout(welcome_group)
        self.welcome_text = QtWidgets.QTextEdit()
        self.welcome_text.setMaximumHeight(80)
        welcome_text_content = self.cfg.get("maintenance", "welcome_text", fallback="欢迎使用维修服务！")
        self.welcome_text.setPlainText(welcome_text_content)

        welcome_copy_btn = QtWidgets.QPushButton("Copy")
        welcome_copy_btn.setMinimumHeight(32)
        welcome_copy_btn.setMaximumWidth(80)
        welcome_copy_btn.clicked.connect(lambda: self._copy_text("维修-欢迎词", self.welcome_text.toPlainText(), include_in_stats=False))

        welcome_layout.addWidget(self.welcome_text, 1)
        welcome_layout.addWidget(welcome_copy_btn)
        layout.addWidget(welcome_group)

        # Group 2: TBD text (moved up from Group 3)
        tbd_group = QtWidgets.QGroupBox("TBD")
        tbd_group.setStyleSheet("""
        QGroupBox {
            background-color: #f6ffed;  /* 淡绿色 */
            border: 2px solid #52c41a;
            border-radius: 5px;
            margin-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px 0 3px;
            color: #52c41a;
            font-weight: bold;
        }
        """)

        tbd_layout = QtWidgets.QHBoxLayout(tbd_group)
        self.tbd_text = QtWidgets.QTextEdit()
        self.tbd_text.setMaximumHeight(80)
        tbd_text_content = self.cfg.get("maintenance", "tbd_text", fallback="待定内容")
        self.tbd_text.setPlainText(tbd_text_content)

        tbd_copy_btn = QtWidgets.QPushButton("Copy")
        tbd_copy_btn.setMinimumHeight(32)
        tbd_copy_btn.setMaximumWidth(80)
        tbd_copy_btn.clicked.connect(lambda: self._copy_text("维修-TBD", self.tbd_text.toPlainText(), include_in_stats=False))

        tbd_layout.addWidget(self.tbd_text, 1)
        tbd_layout.addWidget(tbd_copy_btn)
        layout.addWidget(tbd_group)

        # Group 3: Image display (moved down from Group 2)
        image_group = QtWidgets.QGroupBox("图片")
        image_group.setStyleSheet("""
        QGroupBox {
            background-color: #fffbe6;  /* 淡黄色 */
            border: 2px solid #faad14;
            border-radius: 5px;
            margin-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px 0 3px;
            color: #faad14;
            font-weight: bold;
        }
        """)

        image_layout = QtWidgets.QHBoxLayout(image_group)
        self.image_label = QtWidgets.QLabel()
        self.image_label.setMinimumSize(200, 150)
        self.image_label.setStyleSheet("border: 1px solid #ccc; background-color: white;")
        self.image_label.setAlignment(QtCore.Qt.AlignCenter)
        self.image_label.setText("无图片")
        self.image_label.setScaledContents(False)

        # Load default image if configured
        default_image_path = self.cfg.get("maintenance", "default_image", fallback="")
        if default_image_path and os.path.exists(default_image_path):
            self._load_image(default_image_path)

        image_copy_btn = QtWidgets.QPushButton("Copy")
        image_copy_btn.setMinimumHeight(32)
        image_copy_btn.setMaximumWidth(80)
        image_copy_btn.clicked.connect(self._copy_image)

        image_layout.addWidget(self.image_label, 1)
        image_layout.addWidget(image_copy_btn)
        layout.addWidget(image_group)

        # Group 4: Open directory button
        dir_group = QtWidgets.QGroupBox("工具目录")
        dir_group.setStyleSheet("""
        QGroupBox {
            background-color: #f9f0ff;  /* 淡紫色 */
            border: 2px solid #722ed1;
            border-radius: 5px;
            margin-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px 0 3px;
            color: #722ed1;
            font-weight: bold;
        }
        """)

        dir_layout = QtWidgets.QHBoxLayout(dir_group)
        default_dir = self.cfg.get("maintenance", "default_directory", fallback="")
        dir_btn = QtWidgets.QPushButton(f"打开目录: {os.path.basename(default_dir) if default_dir else '未配置'}")
        dir_btn.setMinimumHeight(40)
        dir_btn.clicked.connect(self._open_maintenance_directory)

        dir_layout.addWidget(dir_btn)
        layout.addWidget(dir_group)

        layout.addStretch()
        return w

    def _load_image(self, image_path):
        """Load and display image in the image label"""
        if os.path.exists(image_path):
            pixmap = QtGui.QPixmap(image_path)
            if not pixmap.isNull():
                # Scale image to fit label while maintaining aspect ratio
                scaled_pixmap = pixmap.scaled(
                    self.image_label.size(),
                    QtCore.Qt.KeepAspectRatio,
                    QtCore.Qt.SmoothTransformation
                )
                self.image_label.setPixmap(scaled_pixmap)
                self.current_image_path = image_path
            else:
                self.image_label.setText("图片加载失败")
                self.current_image_path = None
        else:
            self.image_label.setText("图片文件不存在")
            self.current_image_path = None

    def _copy_image(self):
        """Copy the current image to clipboard"""
        default_image_path = self.cfg.get("maintenance", "default_image", fallback="")
        if default_image_path and os.path.exists(default_image_path):
            pixmap = QtGui.QPixmap(default_image_path)
            if not pixmap.isNull():
                cb = QtWidgets.QApplication.clipboard()
                cb.setPixmap(pixmap)
                append_log(self.log_path, action="copy", group="维修-图片", detail=f"图片: {os.path.basename(default_image_path)}", extra={"include_in_stats": False})
                QtWidgets.QMessageBox.information(self, "复制成功", "图片已复制到剪切板")
            else:
                QtWidgets.QMessageBox.warning(self, "复制失败", "无法加载图片")
        else:
            QtWidgets.QMessageBox.warning(self, "复制失败", "未配置默认图片或图片不存在")

    def _open_maintenance_directory(self):
        """Open the configured maintenance directory"""
        default_dir = self.cfg.get("maintenance", "default_directory", fallback="")
        if default_dir:
            # Normalize path separators for Windows
            normalized_dir = os.path.normpath(default_dir)

            if os.path.exists(normalized_dir):
                try:
                    import subprocess
                    import platform
                    if platform.system() == "Windows":
                        # Use os.startfile for better Windows compatibility
                        os.startfile(normalized_dir)
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", normalized_dir], check=True)
                    else:  # Linux
                        subprocess.run(["xdg-open", normalized_dir], check=True)
                    append_log(self.log_path, action="open_directory", group="维修-目录", detail=normalized_dir, extra={"include_in_stats": False})
                except Exception as e:
                    QtWidgets.QMessageBox.critical(self, "打开失败", f"无法打开目录: {str(e)}")
            else:
                QtWidgets.QMessageBox.warning(self, "打开失败", f"目录不存在: {normalized_dir}")
        else:
            QtWidgets.QMessageBox.warning(self, "打开失败", "未配置默认目录")


def run_app() -> int:
    cfg = load_config()
    log_path = setup_logging(cfg)

    app = QtWidgets.QApplication([])
    # Background application
    palette = app.palette()
    mode = cfg.get("background", "mode", fallback="color")
    if mode == "color":
        color = cfg.get("background", "color", fallback="#ffffff")
        palette.setColor(QtGui.QPalette.Window, QtGui.QColor(color))
        app.setPalette(palette)
    else:
        # Simple image background on the main window only (for now)
        img = cfg.get("background", "image", fallback="")
        layout_mode = cfg.get("background", "layout", fallback="center")
        brightness = float(cfg.get("background", "brightness", fallback="1.0"))
    # Images/layout/brightness can be refined in future iterations

    w = MainWindow(cfg, log_path)
    w.show()
    return app.exec_()

