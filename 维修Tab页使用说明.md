# 维修Tab页功能说明

## 概述
已成功在IT Help Desk Access Tool中添加了新的"维修"Tab页，位于原有"Betelligent"Tab页的右侧。

## 功能特性

### 1. 欢迎词组件
- **位置**: 第一个Group框
- **功能**: 显示可配置的欢迎文本
- **特点**: 
  - 文本内容可在Settings中配置
  - 右侧有"Copy"按钮，点击可将文本复制到剪切板
  - 支持多行文本显示

### 2. 图片组件
- **位置**: 第二个Group框
- **功能**: 显示可配置的默认图片
- **特点**:
  - 图片路径可在Settings中配置
  - 右侧有"Copy"按钮，点击可将**原图**复制到剪切板（非缩略图）
  - 支持常见图片格式：PNG, JPG, JPEG, BMP, GIF
  - 自动缩放显示以适应界面

### 3. TBD组件
- **位置**: 第三个Group框
- **功能**: 显示可配置的TBD（待定）内容
- **特点**:
  - 文本内容可在Settings中配置
  - 右侧有"Copy"按钮，点击可将文本复制到剪切板
  - 支持多行文本显示

### 4. 工具目录组件
- **位置**: 第四个Group框
- **功能**: 打开配置的本地目录
- **特点**:
  - 目录路径可在Settings中配置
  - 点击按钮可直接打开配置的目录
  - 支持Windows资源管理器打开
  - 按钮显示目录名称

## 配置方法

### 通过Settings菜单配置
1. 点击菜单栏的"Set" → "Open Configuration"
2. 在配置窗口中选择"维修"Tab页
3. 可配置以下项目：
   - **欢迎词**: 输入欢迎文本内容
   - **默认图片**: 选择要显示的图片文件
   - **TBD内容**: 输入TBD文本内容
   - **默认目录**: 选择要打开的目录路径
4. 点击"保存"按钮保存配置

### 配置文件直接编辑
也可以直接编辑`config/config.ini`文件中的`[maintenance]`部分：

```ini
[maintenance]
welcome_text = 欢迎使用维修服务！
    这是一个测试欢迎词。
default_image = C:\path\to\your\image.png
tbd_text = 这是TBD测试内容
    可以配置任何需要的信息。
default_directory = C:\path\to\your\directory
```

## 使用流程

### 日常使用
1. 启动应用程序
2. 点击"维修"Tab页
3. 根据需要使用各个功能：
   - 复制欢迎词到剪切板
   - 复制图片到剪切板（用于粘贴到其他应用）
   - 复制TBD内容到剪切板
   - 打开工具目录进行文件操作

### 配置更新
1. 通过Settings菜单更新配置
2. 保存后，维修Tab页内容会自动刷新
3. 无需重启应用程序

## 技术特点

### 界面设计
- 采用与原有Tab页一致的设计风格
- 每个Group使用不同颜色主题便于区分：
  - 欢迎词：淡蓝色主题
  - 图片：淡黄色主题  
  - TBD：淡绿色主题
  - 工具目录：淡紫色主题

### 功能实现
- **剪切板操作**: 使用PyQt5的QApplication.clipboard()
- **图片处理**: 支持原图复制，非缩略图
- **目录打开**: 跨平台支持（Windows/macOS/Linux）
- **配置管理**: 与现有配置系统完全集成
- **日志记录**: 所有操作都会记录到日志文件

### 错误处理
- 图片文件不存在时显示提示信息
- 目录不存在时显示警告对话框
- 配置项缺失时使用默认值
- 操作失败时显示具体错误信息

## 测试验证

已创建测试脚本`test_maintenance.py`用于：
- 创建测试图片文件
- 创建测试目录
- 更新配置文件
- 验证功能完整性

运行测试：
```bash
python test_maintenance.py
python main.py
```

## 注意事项

1. **图片格式**: 建议使用PNG或JPG格式的图片
2. **路径配置**: 使用绝对路径以确保稳定性
3. **权限要求**: 确保应用程序有权限访问配置的目录
4. **文件大小**: 建议图片文件不要过大，以免影响界面响应速度

## 扩展可能

该维修Tab页设计具有良好的扩展性，未来可以：
- 添加更多Group组件
- 支持更多文件类型的复制
- 集成更多工具功能
- 添加快捷键支持
