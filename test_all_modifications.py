#!/usr/bin/env python3
"""
Test script to verify all 5 requested modifications
"""
import os
import json
from datetime import datetime, timezone

def test_log_clearing():
    """Test that logs have been cleared"""
    print("🧹 测试日志清空")
    print("=" * 50)
    
    log_path = os.path.join("logs", "app.log")
    if os.path.exists(log_path):
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                print("✅ 日志文件已清空")
            else:
                lines = content.split('\n')
                print(f"📊 日志文件包含 {len(lines)} 条记录")
                # Check if only sendmail records exist
                sendmail_count = 0
                for line in lines:
                    if line.strip():
                        try:
                            record = json.loads(line)
                            if record.get("action") == "sendmail":
                                sendmail_count += 1
                        except:
                            pass
                print(f"📧 其中发送邮件记录: {sendmail_count} 条")
    else:
        print("❌ 日志文件不存在")
    print()

def test_stats_modification():
    """Test stats.py modifications"""
    print("📊 测试统计功能修改")
    print("=" * 50)
    
    stats_path = os.path.join("tools", "stats.py")
    if os.path.exists(stats_path):
        with open(stats_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for key modifications
        checks = [
            ("filter_sendmail_records", "✅ 过滤函数已更新为sendmail记录"),
            ("plt.plot", "✅ 图表类型已改为折线图"),
            ("Last 30 Days", "✅ 标题已更新为30天统计"),
            ("default=\"month\"", "✅ 默认统计周期已改为月")
        ]
        
        for check, message in checks:
            if check in content:
                print(message)
            else:
                print(f"❌ 未找到: {check}")
    else:
        print("❌ stats.py文件不存在")
    print()

def create_test_sendmail_log():
    """Create test sendmail log entries"""
    print("📧 创建测试发送邮件日志")
    print("=" * 50)
    
    log_path = os.path.join("logs", "app.log")
    test_entries = [
        {
            "ts": datetime.now(timezone.utc).isoformat().replace("+00:00", "Z"),
            "action": "sendmail",
            "group": "STEP5-Sendmail",
            "detail": "<EMAIL>; <EMAIL>",
            "extra": {"bcc": False}
        },
        {
            "ts": datetime.now(timezone.utc).isoformat().replace("+00:00", "Z"),
            "action": "sendmail", 
            "group": "STEP5-Sendmail",
            "detail": "<EMAIL>; <EMAIL>",
            "extra": {"bcc": True}
        }
    ]
    
    try:
        with open(log_path, 'w', encoding='utf-8') as f:
            for entry in test_entries:
                f.write(json.dumps(entry) + '\n')
        print(f"✅ 已创建 {len(test_entries)} 条测试发送邮件日志")
    except Exception as e:
        print(f"❌ 创建测试日志失败: {e}")
    print()

def show_modifications_summary():
    """Show summary of all 5 modifications"""
    print("📋 5项修改内容总结")
    print("=" * 60)
    
    print("1. ✅ 维修Tab页图片组件位置调整")
    print("   - 图片组件已移动到欢迎词组件下方")
    print("   - 新顺序: 欢迎词 → 图片 → Netskope检查 → 工具目录")
    print()
    
    print("2. ✅ 清空现有日志，更新日志逻辑")
    print("   - 已清空原有日志文件")
    print("   - 复制操作不再记录到日志")
    print("   - 仅记录发送邮件操作")
    print()
    
    print("3. ✅ 修改统计功能")
    print("   - 统计范围改为30天(1个月)")
    print("   - 柱形图改为折线图")
    print("   - 只统计发送邮件情况")
    print("   - 邮件Identification去重")
    print()
    
    print("4. ✅ STEP5发送列表始终显示日志")
    print("   - 启动程序时自动加载发送日志")
    print("   - 发送列表始终显示最近15条发送记录")
    print()
    
    print("5. ✅ Send HTML Via Outlook按钮重复发送检查")
    print("   - 检查Email的Identification是否已发送过")
    print("   - 如已发送过，弹窗确认是否继续")
    print("   - 用户可选择继续发送或取消")
    print()

def test_ui_changes():
    """Test UI changes"""
    print("🎨 界面变化测试")
    print("=" * 50)
    
    print("维修Tab页组件顺序 (从上到下):")
    print("  1. 欢迎词 (Group 1)")
    print("  2. 图片 (Group 2) - 已移动到欢迎词下方")
    print("  3. Netskope检查 (Group 3) - 原TBD组件")
    print("  4. 工具目录 (Group 4)")
    print()
    
    print("Betelligent Tab页:")
    print("  - STEP 1: User (包含identification显示)")
    print("  - STEP 2: Add Internal Organization")
    print("  - STEP 4: Teams")
    print("  - STEP 5: Sendmail (包含重复发送检查)")
    print()

if __name__ == "__main__":
    print("🔧 IT Help Desk Access Tool - 5项修改验证")
    print("=" * 70)
    print()
    
    test_log_clearing()
    test_stats_modification()
    create_test_sendmail_log()
    show_modifications_summary()
    test_ui_changes()
    
    print("🚀 测试建议:")
    print("1. 运行 'python main.py' 启动应用程序")
    print("2. 检查维修Tab页组件顺序是否正确")
    print("3. 在Betelligent Tab页测试邮箱输入和发送功能")
    print("4. 确认STEP5列表显示发送日志")
    print("5. 测试重复发送检查功能")
    print("6. 运行 'python tools/stats.py' 查看统计图表")
    print()
    print("✅ 所有5项修改已完成！")
