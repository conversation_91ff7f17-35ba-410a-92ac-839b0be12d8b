@echo off
setlocal enabledelayedexpansion

REM Define Path
set paths="C:\ProgramData\netskope\stagent\data" "%appdata%\Netskope\STAgent"

REM 
for %%p in (%paths%) do (
    set filepath=%%p\npaccesscert_cn.pem
    if exist "!filepath!" (
        del /f /q "!filepath!"
        if exist "!filepath!" (
            echo Deletion Failed: "!filepath!"
        ) else (
            echo Deletion Success: "!filepath!"
        )
    ) else (
        echo File not exist: "!filepath!"
    )
)

pause
